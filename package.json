{"name": "transform-client", "version": "0.1.0", "private": true, "scripts": {"prebuild": "rm -rf dist && basketry", "build": "bash build.sh", "build:basketry": "basketry", "check-env": "node -e 'console.log(process.env)' | grep npm", "lint": "true || eslint .", "lint:fix": "true || eslint . --fix", "dev": "./scripts/dev.sh", "start": "node ./build/", "start:injected": "REACT_APP_INJECTABLE=true BROWSER=none PORT=${PORT:-${npm_config_port:-8101}} WDS_SOCKET_PORT=${PORT:-${npm_config_port:-8101}} REACT_APP_CLOSE_API_PORT=${CLOSE_API_PORT:-3000} webpack serve --config webpack.dev.js ", "start:proxy": "npm run dev", "start:proxy:all": "npm run dev --close --lambdas", "start:standalone": "npm run start:proxy:all", "pretest": "npx playwright install", "test": "vitest", "test:update": "vitest -u", "prepare": "node .husky/install.mjs", "test:browser": "vitest --workspace=vitest.workspace.ts", "sort-translations": "node scripts/sort-translations.mjs", "validate-translations": "node scripts/validate-translations.mjs"}, "dependencies": {"@codemirror/lang-python": "^6.1.7", "@codemirror/lang-sql": "^6.8.0", "@dnd-kit/core": "^6.3.1", "@floqastinc/auth-module-client": "^1.9.0", "@floqastinc/flow-ui_composite": "^0.11.43", "@floqastinc/flow-ui_core": "^3.67.0", "@floqastinc/flow-ui_icons": "^0.32.0", "@floqastinc/fq-intl": "^3.8.1", "@floqastinc/fq-intl-react": "^2.0.0", "@floqastinc/transform-v0": "^0.7.0", "@floqastinc/transform-v2": "2.9.1", "@floqastinc/transform-v3": "3.45.0", "@tanstack/react-query": "^5.65.1", "@types/react": "^19.0.1", "ag-grid-community": "^33.0.4", "ag-grid-react": "^33.0.4", "axios": "^1.8.2", "basketry": "^0.1.3", "codemirror": "^6.0.1", "es-toolkit": "^1.33.0", "i18next": "^23.11.5", "jotai": "^2.9.3", "jotai-scope": "^0.7.2", "node-sql-parser": "^5.3.6", "react": "^18.3.1", "react-archer": "^4.4.0", "react-dom": "^18.3.1", "react-dropzone": "^14.2.9", "react-error-boundary": "^4.0.13", "react-loading-skeleton": "^3.4.0", "react-markdown": "^9.0.1", "react-pdf": "^10.0.1", "react-resizable-panels": "^2.1.2", "react-router-dom": "^6.28.1", "react-type-animation": "^3.2.0", "rowsncolumns": "file:rowsncolumns-1.0.0.tgz", "single-spa-react": "^6.0.2", "styled-components": "^6.1.14", "ts-pattern": "^5.3.1", "xlsx": "https://cdn.sheetjs.com/xlsx-0.20.3/xlsx-0.20.3.tgz", "zod": "^3.25.20"}, "devDependencies": {"@basketry/express": "^0.2.0-rc.4", "@basketry/openapi-3": "^0.1.2", "@basketry/react-query": "^0.1.0-alpha.9", "@basketry/typescript": "^0.1.2", "@eslint/eslintrc": "^3.3.0", "@eslint/js": "^9.21.0", "@floqastinc/eslint-config-i18next": "^1.2.0", "@floqastinc/eslint-plugin-validate-i18next": "^0.0.2", "@tanstack/react-query-devtools": "^5.40.1", "@testing-library/dom": "^10.4.0", "@types/node": "^22.10.5", "@types/react": "^19.0.4", "@types/react-dom": "^19.0.2", "@vitejs/plugin-react": "^4.3.4", "@vitest/browser": "^3.0.9", "ajv": "^8.17.1", "basketry": "^0.1.3", "compression-webpack-plugin": "^11.1.0", "css-loader": "^7.1.2", "dotenv": "^16.4.5", "dotenv-webpack": "^8.1.0", "env-cmd": "^10.1.0", "esbuild-loader": "^4.3.0", "eslint": "^9.21.0", "eslint-config-prettier": "^10.1.1", "eslint-import-resolver-typescript": "^3.8.3", "eslint-plugin-i18next": "^6.1.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-import-x": "^4.6.1", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "file-loader": "^6.2.0", "globals": "^16.0.0", "happy-dom": "^18.0.1", "html-webpack-plugin": "^5.6.3", "http-proxy-middleware": "^2.0.6", "husky": "^9.1.7", "jsdom": "^26.1.0", "lint-staged": "^15.4.3", "mini-css-extract-plugin": "^2.9.2", "playwright": "^1.51.1", "prettier": "^3.2.5", "prop-types": "^15.8.1", "rollup-plugin-serve": "^3.0.0", "standalone-single-spa-webpack-plugin": "^4.0.0", "systemjs-webpack-interop": "^2.3.7", "tsconfig-paths-webpack-plugin": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.24.1", "vite": "^6.2.3", "vite-plugin-environment": "^1.1.3", "vite-plugin-single-spa": "^0.8.1", "vite-plugin-svgr": "^4.3.0", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.1.4", "vitest-browser-react": "^0.1.1", "webpack": "^5.97.1", "webpack-bundle-analyzer": "^4.10.2", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.2.0", "webpack-merge": "^6.0.1"}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "4.13.0"}, "lint-staged": {"*.{js,jsx,ts,tsx,json,md}": ["prettier --write"], "*.{js,jsx,ts,tsx}": ["eslint --fix --no-warn-ignored"], "src/locales/*/translation.json": ["npm run sort-translations", "npm run validate-translations"]}, "browserslist": ["last 4 chrome version", "last 4 firefox version"], "engines": {"node": ">= 16", "npm": ">= 6.x"}, "volta": {"node": "20.14.0"}}