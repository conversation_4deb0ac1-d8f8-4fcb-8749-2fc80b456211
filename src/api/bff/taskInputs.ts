import axios from "axios";
import { NewWorkflowInput } from "@floqastinc/transform-v3";
import v3, { ApiError } from "@/services/v3";
import { featureFlags } from "@/components/FeatureFlag";

const MAX_FILE_SIZE_BYTES = 10 * 1024 * 1024; // 10 MB

export type WorkflowInputData = NewWorkflowInput & {
  //eslint-disable-next-line @typescript-eslint/no-explicit-any
  value: any;
};
type CreateTaskInputParams = {
  workflowId: string;
  taskId: string;
  exampleSetId: string;
  input: WorkflowInputData;
};
export const createTaskInput = async ({
  workflowId,
  taskId,
  exampleSetId,
  input,
}: CreateTaskInputParams) => {
  const workflowInputRes = await v3.workflowInputs.createWorkflowInput({
    workflowId,
    input: {
      name: input.name,
      type: input.type,
    },
  });
  if (workflowInputRes.errors?.length) {
    throw new ApiError(workflowInputRes.errors);
  }
  if (!workflowInputRes.data) {
    throw new Error("Unexpected error: no data or errors returned from createWorkflowInput");
  }

  const { data: workflowInput } = workflowInputRes;

  const taskInputRes = await v3.taskInputs.createTaskInput({
    workflowId,
    taskId,
    input: {
      name: input.name,
      type: input.type,
      description: input.description,
      source: {
        workflowInputId: workflowInput.id,
      },
    },
  });
  if (taskInputRes.errors?.length) {
    throw new ApiError(taskInputRes.errors);
  }
  if (!taskInputRes.data) {
    throw new Error("Unexpected error: no data or errors returned from createTaskInput");
  }

  const exampleInputResponse = await v3.exampleInputs.createExampleInput({
    workflowId,
    taskId,
    exampleSetId,
    input: {
      taskInputId: taskInputRes.data?.id,
    },
  });
  if (exampleInputResponse.errors?.length) {
    throw new ApiError(exampleInputResponse.errors);
  }
  if (!exampleInputResponse.data) {
    throw new Error("Unexpected error: no data or errors returned from createExampleInput");
  }
  const exampleInput = exampleInputResponse.data;

  if (input.type === "DATETIME") {
    const setDateInputRes = await v3.exampleInputs.setExampleInputDatetime({
      workflowId,
      value: {
        kind: "DATETIME",
        value: new Date(input.value),
      },
      taskId,
      exampleSetId,
      exampleInputId: exampleInput.id,
    });
    if (setDateInputRes.errors?.length) {
      throw new ApiError(setDateInputRes.errors);
    }

    return setDateInputRes;
  }

  if (input.type === "TEXT") {
    const setTextInputRes = await v3.exampleInputs.setExampleInputText({
      workflowId,
      taskId,
      exampleSetId,
      exampleInputId: exampleInput.id,
      value: {
        kind: "TEXT",
        value: input.value,
      },
    });
    if (setTextInputRes.errors?.length) {
      throw new ApiError(setTextInputRes.errors);
    }

    return setTextInputRes;
  }

  if (input.type === "NUMBER") {
    const setNumberInputRes = await v3.exampleInputs.setExampleInputNumber({
      workflowId,
      taskId,
      exampleSetId,
      exampleInputId: exampleInput.id,
      value: {
        kind: "NUMBER",
        value: typeof input.value === "string" ? parseFloat(input.value) : input.value,
      },
    });
    if (setNumberInputRes.errors?.length) {
      throw new ApiError(setNumberInputRes.errors);
    }

    return setNumberInputRes;
  }

  if (input.type === "FILE") {
    // Early exit if file is too large
    if (input.value.size > MAX_FILE_SIZE_BYTES) {
      throw new ApiError([
        {
          status: 413,
          code: "FILE_TOO_LARGE",
          title: "File Too Large, must be less than 10MB",
        },
      ]);
    }

    const inputResponse = await v3.exampleInputs.setExampleInputFileValue({
      workflowId,
      taskId,
      exampleSetId,
      exampleInputId: exampleInputResponse.data.id,
      value: {
        kind: "FILE",
        mimetype: input.value.type,
        name: input.value.name,
      },
    });
    if (inputResponse.errors.length > 0) {
      return inputResponse.errors;
    }
    if (!inputResponse.data) {
      throw new Error(
        "Unexpected error: Failed to set example input file value, but no errors were generated",
      );
    }

    const { url } = inputResponse.data;
    const s3Res = await axios.put(url, input.value, {
      headers: {
        "Content-Type": input.value.type,
      },
    });
    if (s3Res.status !== 200) {
      const { errors: deleteWorkflowInputErrors } = await v3.workflowInputs.deleteWorkflowInput({
        workflowId,
        workflowInputId: workflowInput.id,
      });
      if (deleteWorkflowInputErrors.length) {
        throw new ApiError(deleteWorkflowInputErrors);
      }
      const { errors: deleteTaskInputErrors } = await v3.taskInputs.deleteTaskInput({
        workflowId,
        taskId,
        taskInputId: taskInputRes.data.id,
      });
      if (deleteTaskInputErrors.length) {
        throw new ApiError(deleteTaskInputErrors);
      }
      throw new Error("Failed to upload file: " + s3Res.statusText);
    }
    // TODO: Guard against API Timeout: GUCCI-865
    if (featureFlags.get("file-context-enabled")) {
      try {
        await v3.fileContext.createFileContext({
          workflowId,
          taskId,
          exampleSetId,
          exampleInputId: exampleInputResponse.data.id,
        });
      } catch (e) {
        console.error(`Error creating file context for workflow ${workflowId}, task ${taskId}:`, e);
      }
    } else {
      console.log("File context is disabled.");
    }
    // if data-sampling is undefined or false, skip creating a file sample
    if (featureFlags.get("data-sampling")) {
      try {
        await v3.fileSamples.createFileSample({
          workflowId,
          taskId,
          exampleSetId,
          exampleInputId: exampleInput.id,
        });
      } catch (e) {
        console.error(e);
      }
    }

    return;
  }

  console.error("Unsupported input type", input.type);
};

export const cleanupInputFile = async ({
  workflowId,
  taskId,
  exampleSetId,
  exampleInputId,
}: {
  workflowId: string;
  taskId: string;
  exampleSetId: string;
  exampleInputId: string;
}) => {
  try {
    const cleanupResponse = await v3.fileSamples.cleanupInputFile({
      workflowId,
      taskId,
      exampleSetId,
      exampleInputId,
    });

    if (cleanupResponse.errors?.length) {
      throw new ApiError(cleanupResponse.errors);
    }

    if (!cleanupResponse.data) {
      throw new Error("Unexpected error: no data or errors returned from cleanup operation");
    }

    return cleanupResponse;
  } catch (error) {
    if (error instanceof ApiError) {
      throw error;
    }
    throw new Error(
      `Failed to cleanup input file for workflow ${workflowId}, example set ${exampleSetId}, example input ${exampleInputId}`,
    );
  }
};

export const validateFileSize = async ({
  workflowId,
  taskId,
  exampleSetId,
  exampleInputId,
}: {
  workflowId: string;
  taskId: string;
  exampleSetId: string;
  exampleInputId: string;
}) => {
  try {
    const validationResponse = await v3.fileSamples.handleGetFileSize({
      workflowId,
      taskId,
      exampleSetId,
      exampleInputId,
    });

    if (validationResponse.errors?.length) {
      throw new ApiError(validationResponse.errors);
    }

    if (!validationResponse.data) {
      throw new Error("Unexpected error: no data or errors returned from file size validation");
    }

    return validationResponse;
  } catch (error) {
    if (error instanceof ApiError) {
      throw error;
    }
    throw new Error(
      `Failed to validate file size for workflow ${workflowId}, example set ${exampleSetId}, example input ${exampleInputId}`,
    );
  }
};

export const removeInputFileValue = async ({
  workflowId,
  inputValueId,
}: {
  workflowId: string;
  inputValueId: string;
}) => {
  try {
    const removeInputFileValueResponse = await v3.fileSamples.removeInputFileValue({
      workflowId,
      inputValueId,
    });

    if (!removeInputFileValueResponse.data) {
      throw new Error("Unexpected error: no data or errors returned from removeInputFileValue");
    }

    if (removeInputFileValueResponse.errors?.length) {
      throw new ApiError(removeInputFileValueResponse.errors);
    }

    return removeInputFileValueResponse;
  } catch (error) {
    if (error instanceof ApiError) {
      throw error;
    }
    throw new Error(
      `Failed to remove input file value for workflow ${workflowId}, input value ${inputValueId}`,
    );
  }
};
