import { useNavigate } from "react-router-dom";
import { useFeatureFlags } from "@/components/FeatureFlag";
import { AGENTS, BUILDER, STEPS, V3 } from "@/constants";
import { useDuplicateWorkflow } from "@v3/workflows";

export const useEditAgent = () => {
  const { getFlag } = useFeatureFlags();
  const isWorkflowVersioningEnabled = getFlag("enable-workflow-versioning");
  const navigate = useNavigate();
  const duplicateWorkflow = useDuplicateWorkflow();

  const handleEditAgent = async (agentId: string, stepId?: string) => {
    if (!isWorkflowVersioningEnabled) {
      navigate(`/builder/v3/${AGENTS}/${agentId}`);
      return;
    }

    const duplicatedWorklow = await duplicateWorkflow.mutateAsync({ workflowId: agentId });

    navigate(
      `/${BUILDER}/${V3}/${AGENTS}/${duplicatedWorklow.id}` + (stepId ? `/${STEPS}/${stepId}` : ""),
    );
  };

  return handleEditAgent;
};
