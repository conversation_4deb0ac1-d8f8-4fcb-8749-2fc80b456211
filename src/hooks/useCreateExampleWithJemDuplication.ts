import { v3 } from '@/services/v3';
import duplicateJemTemplate from '@Transform/api/duplicate-jem-template';
import { useCreateExample } from '@v3/examples';

type Args = Parameters<typeof useCreateExample>[0];

/**
 * This hook can be used to create a new example, and duplicate the jem template if the example is copied from an existing JEM example set.
 */
export const useCreateExampleWithJemDuplication = (args: Args) =>
  useCreateExample({
    ...args,
    onMutate: async ({ workflowId, taskId, example }) => {
      if (example.copyFromExampleSetId !== undefined) {
        const { data: task } = await v3.tasks.getTask({
          workflowId,
          taskId,
        });

        if (task?.strategy?.kind === 'JEM_TEMPLATE_FETCH') {
          await duplicateJemTemplate(taskId, example.copyFromExampleSetId, workflowId);
        }
      }
    },
  });
