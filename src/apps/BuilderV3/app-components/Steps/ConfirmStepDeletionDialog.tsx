import { Dialog, Spinner } from "@floqastinc/flow-ui_core";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useNavigate, useParams } from "react-router";
import { Task } from "@floqastinc/transform-v3";
import { t } from "@/utils/i18n";
import { queryClient } from "@/components/queryClient";
import { deleteTaskMutation, getWorkflowTasksQuery } from "@BuilderV3/api/tasks";
import { formatTaskDate } from "@/utils/date";
import { BUILDER, STEPS, V3, AGENTS } from "@/constants";

type ConfirmStepDeletionDialogProps = {
  isOpen: boolean;
  task: Task;
  deleteType: "delete-step" | "delete-step-and-after" | null;
  onConfirm: () => void;
  onOpenChange: (isOpen: boolean) => void;
};
export const ConfirmStepDeletionDialog = ({
  isOpen,
  task,
  deleteType,
  onConfirm,
  onOpenChange,
}: ConfirmStepDeletionDialogProps) => {
  const { workflowId, taskId, exampleSetId } = useParams();
  if (!workflowId || !taskId || !exampleSetId) {
    console.error(t("components.ConfirmStepDeletionDialog.Errors.noIDsprovided"));
    throw new Error(t("components.ConfirmStepDeletionDialog.Errors.noIDsprovided"));
  }
  const navigate = useNavigate();

  const tasksQuery = useQuery(getWorkflowTasksQuery(workflowId));
  const tasks = tasksQuery.data ?? [];

  const deleteTask = useMutation({
    mutationKey: ["deleteTask", { workflowId, taskId }],
    mutationFn: deleteTaskMutation,
    onSuccess: (prevTask) => {
      queryClient.invalidateQueries({
        queryKey: [{ scope: "workflows", filter: { workflowId } }],
      });
      queryClient.invalidateQueries({
        queryKey: [{ scope: "tasks", filter: { workflowId } }],
      });

      // Optionally, we can also update the tasks query cache directly
      queryClient.setQueryData(
        getWorkflowTasksQuery(workflowId).queryKey,
        (oldData: Task[] | undefined) => {
          if (!oldData) return [];
          return oldData.filter((task) => task.id !== taskId);
        },
      );

      console.log("prevTask", prevTask);
      onOpenChange(false);
      onConfirm();

      if (prevTask) {
        navigate(`/${BUILDER}/${V3}/${AGENTS}/${workflowId}/${STEPS}/${prevTask.id}`, {
          replace: true,
        });
      } else {
        navigate(`/${BUILDER}/${V3}/${AGENTS}/${workflowId}`, {
          replace: true,
        });
      }
    },
    onError: (error) => {
      console.error("error", error);
    },
  });

  const isDeleteStepAndAfter = deleteType === "delete-step-and-after";
  const dialogTitle = isDeleteStepAndAfter
    ? t("components.ConfirmStepDeletionDialog.deleteStepAfter")
    : t("components.ConfirmStepDeletionDialog.deleteThisStep");

  let dialogMessage;
  if (isDeleteStepAndAfter) {
    const firstMessage = t("components.ConfirmStepDeletionDialog.aboutToDeleteStepCascade", {
      taskName: task.name ? `"${task.name}"` : "",
      taskCreatedAt: task.createdAt ? formatTaskDate(task.createdAt) : "",
    });
    const secondMessage = task.createdAt
      ? ""
      : t("components.ConfirmStepDeletionDialog.aPreviousStep");

    dialogMessage = `${firstMessage} ${secondMessage} ${t("components.ConfirmStepDeletionDialog.willRemoveAllStepsAfter")}`;
  } else {
    const firstMessage = t("components.ConfirmStepDeletionDialog.aboutToDeleteStep", {
      taskName: task.name ? `"${task.name}"` : "",
      taskCreatedAt: task.createdAt ? formatTaskDate(task.createdAt) : "",
    });
    const secondMessage = task.createdAt
      ? ""
      : t("components.ConfirmStepDeletionDialog.aPreviousStep");

    dialogMessage = `${firstMessage} ${secondMessage} ${t("components.ConfirmStepDeletionDialog.willRemoveStep")}`;
  }

  return (
    <Dialog type="warning" open={isOpen} onOpenChange={onOpenChange}>
      <Dialog.Header>{dialogTitle}</Dialog.Header>
      <Dialog.Body>{dialogMessage}</Dialog.Body>
      <Dialog.Footer>
        <Dialog.FooterCancelBtn onClick={() => onOpenChange(false)}>
          {t("components.ConfirmStepDeletionDialog.cancel")}
        </Dialog.FooterCancelBtn>
        <Dialog.FooterActionBtn
          disabled={deleteTask.isPending || tasksQuery.isPending}
          onClick={() =>
            deleteTask.mutate({
              workflowId,
              taskId: task.id,
              tasks,
              deleteAfter: isDeleteStepAndAfter,
            })
          }
        >
          {deleteTask.isPending || tasksQuery.isPending ? (
            <Spinner style={{ position: "absolute" }} color="success" size={32} />
          ) : null}
          {t("components.ConfirmStepDeletionDialog.delete")}
        </Dialog.FooterActionBtn>
      </Dialog.Footer>
    </Dialog>
  );
};
