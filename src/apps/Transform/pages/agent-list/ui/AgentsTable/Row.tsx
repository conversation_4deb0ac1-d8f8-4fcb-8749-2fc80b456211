import { forwardRef, useState } from "react";
import { TR, TD, Button, IconButton, Flex, Tooltip, DropdownPanel } from "@floqastinc/flow-ui_core";
import MoreVert from "@floqastinc/flow-ui_icons/material/MoreVert";
import MaterialEditIcon from "@floqastinc/flow-ui_icons/material/Edit";
import MaterialDeleteIcon from "@floqastinc/flow-ui_icons/material/Delete";
import Coupa from "@floqastinc/flow-ui_icons/fq/Coupa";
import Netsuite from "@floqastinc/flow-ui_icons/fq/Netsuite";
import Intacct from "@floqastinc/flow-ui_icons/fq/Intacct";
import Workday from "@floqastinc/flow-ui_icons/fq/Workday";
import TableOutlined from "@floqastinc/flow-ui_icons/material/TableOutlined";
import SearchActivity from "@floqastinc/flow-ui_icons/material/SearchActivity";
import { styled } from "styled-components";
import { useNavigate } from "react-router-dom";
import { Workflow } from "@floqastinc/transform-v3";
import { match } from "ts-pattern";
import { useMutation } from "@tanstack/react-query";
import { ProtectedComponent } from "@floqastinc/auth-module-client";
import * as Styled from "./Row.styled";
import { t } from "@/utils/i18n";
import { USER_ACTION_KEYS } from "@/authorization";
import { RUNNER, V3, AGENTS } from "@/constants";
import { Time } from "@/components/Time";
import { ActivityLogSideDrawer } from "@Transform/components/ActivityLogSideDrawer/ActivityLogSideDrawer";
import { v3 } from "@/services/v3";
import { queryClient, useModal } from "@/components";
import { queryKeys } from "@BuilderV3/api/query-keys";
import { useUpdateWorkflow } from "@BuilderV3/routes/workflows/BuilderPage.hooks";

type RowProps = {
  workflow: Workflow;
};

type ConnectionIconProps = {
  connection: string;
};
const ConnectionIcon = ({ connection }: ConnectionIconProps) => {
  return match(connection)
    .with("EXCEL", () => <TableOutlined size={20} />)
    .with("COUPA", () => <Coupa size={20} />)
    .with("NETSUITE", () => <Netsuite size={20} />)
    .with("INTACCT", () => <Intacct size={20} />)
    .with("WORKDAY", () => <Workday size={20} />)
    .otherwise(() => null);
};

const IconWrap = styled.div`
  float: right;
  display: flex;
`;

const DropdownPanelText = styled.span`
  margin-top: 3px;
`;

export const Row = forwardRef<HTMLDivElement, RowProps>(({ workflow }, ref) => {
  const { id: agentId, name, description, latestRun, connectedData, updatedAt } = workflow;

  const [showActivityLog, setShowActivityLog] = useState(false);
  const [isMoreOptionsOpenForWorkflowId, setIsMoreOptionsOpenForWorkflowId] = useState<
    string | null
  >(null);
  const updateWorkflow = useUpdateWorkflow();
  const navigate = useNavigate();
  const { openModal } = useModal();

  const editWorkflow = useMutation({
    mutationFn: (workflow: Workflow) => {
      return v3.workflows.updateWorkflow({
        workflowId: workflow.id,
        workflow,
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: queryKeys.workflows.all(),
      });
    },
  });

  const deleteWorkflow = useMutation({
    mutationFn: (id: string) => {
      return v3.workflows.deleteWorkflow({ workflowId: id });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: queryKeys.workflows.all(),
      });
    },
  });

  return (
    <>
      <TR ref={ref} rowId={agentId.toString()} key={agentId}>
        <TD>
          <TD.Title style={{ "white-space": "normal" }}>{name}</TD.Title>
        </TD>
        <TD>
          <TD.Description>{description}</TD.Description>
        </TD>
        <TD style={{ height: "100%", alignItems: "center" }}>
          <TD.Description>
            <Flex justify="flex-start">
              {(connectedData?.length ?? 0) > 0
                ? connectedData?.map((connection) => (
                    <Styled.Icon key={connection}>
                      <ConnectionIcon connection={connection} />
                    </Styled.Icon>
                  ))
                : "-"}
            </Flex>
          </TD.Description>
        </TD>
        <TD>
          <TD.Description>
            {!latestRun ? "-" : <Time value={latestRun.date} relative />}
          </TD.Description>
        </TD>
        <TD>
          <TD.Description>
            <Time value={updatedAt} relative />
          </TD.Description>
        </TD>
        <ProtectedComponent actionKey={USER_ACTION_KEYS.TRANSFORM_WORKFLOW_WRITE}>
          <TD style={{ padding: "12px 0px 12px 12px" }}>
            <Flex justify="center" align="center" width="0px">
              <Button
                color="dark"
                variant="ghost"
                data-tracking-id="dashboard-edit-agent-button"
                onClick={async () => {
                  // If the agent is active andd does not have a review step, set it to Draft before editing
                  if (workflow.status === "ACTIVE" && !workflow.humanInTheLoop) {
                    workflow.status = "DRAFT";
                    await updateWorkflow.mutateAsync(workflow);
                  }
                  navigate(`/builder/${V3}/${AGENTS}/${agentId}`);
                }}
              >
                {t("components.Row.edit")}
              </Button>
            </Flex>
          </TD>
        </ProtectedComponent>
        <TD style={{ padding: "12px 0px" }}>
          <Flex justify="center" align="center" width="0px">
            <Button
              color="dark"
              variant="outlined"
              data-tracking-id="dashboard-preview-agent-button"
              onClick={() => navigate(`/${RUNNER}/${V3}/${AGENTS}/${agentId}`)}
            >
              {t("components.Row.preview")}
            </Button>
          </Flex>
        </TD>
        <TD style={{ padding: "12px 12px 12px 0px" }}>
          <Flex justify="center" align="center" width="0px">
            {!workflow.humanInTheLoop && workflow.status === "ACTIVE" ? (
              <Tooltip>
                <Tooltip.Trigger>
                  <Button
                    color="dark"
                    data-tracking-id="dashboard-run-agent-button"
                    disabled
                    onClick={() =>
                      navigate(`/${RUNNER}/${V3}/${AGENTS}/${agentId}?run=true`, {
                        state: { history: "run_list" },
                      })
                    }
                  >
                    {t("components.Row.run")}
                  </Button>
                </Tooltip.Trigger>
                <Tooltip.Content
                  hasArrow
                  side="top"
                  style={{
                    whiteSpace: "normal",
                    maxWidth: "150px",
                    wordBreak: "break-word",
                    zIndex: 1000,
                  }}
                >
                  {t("components.Row.oneHumanStep")}
                </Tooltip.Content>
              </Tooltip>
            ) : (
              <Button
                color="dark"
                data-tracking-id="dashboard-run-agent-button"
                onClick={() =>
                  navigate(`/${RUNNER}/${V3}/${AGENTS}/${agentId}?run=true`, {
                    state: { history: "run_list" },
                  })
                }
              >
                {t("components.Row.run")}
              </Button>
            )}
          </Flex>
        </TD>
        <TD style={{ padding: "12px 16px 12px 8px" }}>
          <Tooltip>
            <Tooltip.Trigger aria-label="tooltip-trigger" data-testid="tooltip-trigger">
              <Flex justify="center" align="center" width="0px">
                <IconButton
                  onClick={() => setShowActivityLog(true)}
                  data-tracking-id="dashboard-runner-activity-log-button"
                >
                  <SearchActivity />
                </IconButton>
              </Flex>
            </Tooltip.Trigger>
            <Tooltip.Content aria-label="tooltip-content" data-testid="tooltip-content">
              {t("components.Row.showActivityLog")}
            </Tooltip.Content>
          </Tooltip>
        </TD>
        <ProtectedComponent actionKey={USER_ACTION_KEYS.TRANSFORM_WORKFLOW_FULL}>
          <TD>
            <DropdownPanel
              disableFilter
              disableClear
              isOpen={isMoreOptionsOpenForWorkflowId === workflow.id}
              onOpenChange={(isOpen: boolean) => {
                if (isOpen) {
                  setIsMoreOptionsOpenForWorkflowId(workflow.id);
                } else {
                  setIsMoreOptionsOpenForWorkflowId(null);
                }
              }}
              onChange={(value: string) => {
                match(value)
                  .with("edit-workflow", () => {
                    openModal("EditWorkflow", {
                      workflow,
                      onSave: (workflow: Workflow) => editWorkflow.mutate(workflow),
                    });
                  })
                  .with("delete-workflow", () => {
                    openModal("DeleteWorkflow", {
                      workflowName: workflow.name,
                      onSubmit: () => deleteWorkflow.mutate(workflow.id),
                    });
                  });
                setIsMoreOptionsOpenForWorkflowId(null);
              }}
            >
              <DropdownPanel.Trigger>
                <IconButton
                  size="md"
                  data-tracking-id="dashboard-more-options-dropdown-button"
                  onClick={() => {
                    setIsMoreOptionsOpenForWorkflowId(workflow.id);
                  }}
                >
                  <MoreVert color="#1D2433" />
                </IconButton>
              </DropdownPanel.Trigger>
              <DropdownPanel.Content>
                <DropdownPanel.Option value="edit-workflow" key="edit-workflow">
                  <IconWrap>
                    <MaterialEditIcon color="action" />
                  </IconWrap>
                  <DropdownPanelText>{t("components.Row.editAgent")}</DropdownPanelText>
                </DropdownPanel.Option>
                <DropdownPanel.Option value="delete-workflow" key="delete-workflow">
                  <IconWrap>
                    <MaterialDeleteIcon color="action" />
                  </IconWrap>
                  <DropdownPanelText>{t("components.Row.deleteAgent")}</DropdownPanelText>
                </DropdownPanel.Option>
              </DropdownPanel.Content>
            </DropdownPanel>
          </TD>
        </ProtectedComponent>
      </TR>
      <ActivityLogSideDrawer
        show={showActivityLog}
        agentId={agentId}
        agentName={workflow?.name || ""}
        onClose={() => setShowActivityLog(false)}
      />
    </>
  );
});
Row.displayName = "Row";
