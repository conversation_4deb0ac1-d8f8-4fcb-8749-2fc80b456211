import { styled } from "styled-components";
import { Button } from "@floqastinc/flow-ui_core";

export const SQLEditorContainer = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
  max-height: 45vh;
`;

export const SQLEditorHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

export const SQLEditorActionsContainer = styled.div`
  display: flex;
  align-items: center;
  padding-right: 16px;
`;

export const SQLEditorWrapper = styled.div`
  width: 100%;
  height: 100%;
  max-height: 100%;
  overflow: auto;
  .cm-editor {
    height: 100%;

    border: 1px solid #ddd;
    &.cm-focused {
      outline: none;
    }
  }
  &.loading-schema::before {
    content: "Loading data...";
    position: absolute;
    bottom: 8px;
    right: 8px;
    color: var(--flo-base-color-neutral-500);
    font-family: var(--flo-sem-font-family-body);
    opacity: 0.7;
  }
`;

export const SearchContainer = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 8px;
  padding-top: 8px;
`;

export const ChatContent = styled.div`
  display: flex;
  flex-direction: column;
  position: relative;
  overflow-y: auto;
  overflow-x: hidden;
  align-items: center;
  justify-content: space-between;
  height: 395px;
  width: 100%;
  background-color: #fafbf9;
  scrollbar-width: thin;
`;

// TODO: Check if this background-color exists: looks like the weakest
//  is #f8f which is a bit darker
export const ChatLog = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  padding: 8px;
  justify-content: space-between;
`;

export const ChatLogMessages = styled.div`
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 8px;
  background-color: #fafbf9;
  gap: 24px;
`;

export const ChatMessage = styled.div`
  font-family: var(--flo-base-font-family-2);
  font-size: var(--flo-base-font-size-4);
  font-weight: var(--flo-base-font-weight-4);
  display: flex;
  flex-direction: column;
  max-width: 300px;
  border-radius: 6px;
  padding: 12px;

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-weight: bold;
    font-size: revert;
  }

  & ul,
  & ol {
    list-style: disc inside;
  }

  & p {
    margin: 8px 0;
  }
  *:first-child {
    margin-top: 0;
  }
  *:last-child {
    margin-bottom: 0;
  }
`;

export const UserChatContainer = styled.div`
  display: flex
  flex-direction: column;
  gap: 6px;
  align-self: flex-end;
  width: 100%;
`;

// TODO: find where this background-color
//  is defined in flow-ui (or if it is)
export const UserChat = styled(ChatMessage)`
  display: flex;
  background-color: #efedea;
  justify-self: end;
  max-width: 80%;
`;

export const UserChatActions = styled.div`
  display: flex;
  flex-direction: row;
  justify-self: end;
`;

export const AssistantChat = styled(ChatMessage)`
  display: flex;
  align-self: flex-start;
  word-break: break-word;
  white-space: pre-wrap;
  max-width: 60%;
  border: 1px solid var(--flo-sem-color-border);
`;

export const ChatBox = styled.div`
  display: flex;
  flex-direction: column;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.07);
  position: sticky;
  bottom: 0;
  width: 100%;
  border-radius: 8px;
`;

export const TextAreaActionBar = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 8px;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
  border: 1px solid var(--flo-sem-color-border);
  border-top: none;
  background-color: var(--flo-sem-color-light-background);
`;

export const SubmitChatButton = styled(Button)`
  height: 32px;
  width: 32px;
  padding: 8px;
`;
