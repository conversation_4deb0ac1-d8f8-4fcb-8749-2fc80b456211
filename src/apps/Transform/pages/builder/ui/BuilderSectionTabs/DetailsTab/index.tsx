import { useEffect, useMemo, useState } from "react";
import { Button, useToast, EmptyState, Text } from "@floqastinc/flow-ui_core";
import cloneDeep from "lodash/cloneDeep";
import { TaskDescription, TaskDescriptionDescription } from "@floqastinc/transform-v3";
import { useMutation, useQueryClient, useQuery } from "@tanstack/react-query";
import { isEqual } from "es-toolkit";
import { DetailSteps } from "../DetailStep/DetailSteps";
import * as Styled from "./styles";
import DetailsCard from "@BuilderV3/routes/workflows/components/DetailsSlideout/DetailsCard";
import { v3 } from "@/services/v3";
import { Loading } from "@/components/Loading";
import { useMessages } from "@v3/messages";
import { t } from "@/utils/i18n";

interface DetailsTabProps {
  workflowId: string;
  taskId: string;
  exampleSetId: string;
}

export const DetailsTab = ({ workflowId, taskId, exampleSetId }: DetailsTabProps) => {
  const [loading, setLoading] = useState(false);
  const [editing, setEditing] = useState(false);
  const [descriptionValue, setDescriptionValue] = useState<TaskDescriptionDescription[]>();

  const { showToast, Toast } = useToast();
  const queryClient = useQueryClient();

  const messagesQuery = useMessages({
    workflowId,
    taskId,
    exampleSetId,
  });

  const hasConversation = !!(
    messagesQuery.isSuccess &&
    messagesQuery.data?.data?.some((m) => m.role === "user") &&
    messagesQuery.data.data.some((m) => m.role === "assistant")
  );

  const fetchDescription = async (): Promise<TaskDescription | null> => {
    setLoading(true);
    let response = await v3.taskDescriptions.getTaskDescriptionByCompositeKey({
      workflowId,
      taskId,
      exampleSetId,
    });
    if (!response?.data?.id) {
      response = await v3.taskDescriptions.createTaskDescription({
        workflowId,
        taskId,
        exampleSetId,
        description: {
          generate: true,
        },
      });
    }
    if (!response?.data?.id) {
      console.error(t("components.DetailsTab.Errors.workflowDescNotFound"), response);
      showToast(
        <Toast type="error">
          <Toast.Title>{t("components.DetailsTab.Errors.somethingWentWrong")}</Toast.Title>
          <Toast.Message>
            {`${t("components.DetailsTab.Errors.unableToGetDescription")} (${response.errors.map((e) => e.detail).join("; ")})`}
          </Toast.Message>
        </Toast>,
        { position: "top-right" },
      );
    }
    setLoading(false);
    return response.data || null;
  };

  const { data } = useQuery({
    queryKey: ["description"],
    queryFn: fetchDescription,
    enabled: hasConversation,
  });

  useEffect(() => {
    setDescriptionValue(cloneDeep(data?.description));
  }, [data?.description]);

  const updateDescription = async ({
    description,
    generate,
  }: {
    description?: TaskDescriptionDescription[];
    generate: boolean;
  }): Promise<TaskDescription | null> => {
    if (!data || !data?.id) return null;
    setLoading(true);
    const response = await v3.taskDescriptions.updateTaskDescription({
      taskDescriptionId: data?.id,
      workflowId,
      taskId,
      exampleSetId,
      description: {
        description: description || [],
        generate: generate,
      },
    });
    return response.data || null;
  };

  const updateDescriptionMutation = useMutation({
    mutationFn: ({
      description,
      generate,
    }: {
      description?: TaskDescriptionDescription[];
      generate: boolean;
    }) => updateDescription({ description, generate }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["description"] });
      setLoading(false);
      showToast(
        <Toast type="success">
          <Toast.Title>{t("components.DetailsTab.success")}</Toast.Title>
          <Toast.Message>{t("components.DetailsTab.updatedTaskDescription")}</Toast.Message>
        </Toast>,
        { position: "top-right" },
      );
    },
    onError: (error) => {
      setLoading(false);
      console.error(t("components.DetailsTab.error"), error);
      showToast(
        <Toast type="error">
          <Toast.Title>{t("components.DetailsTab.error")}</Toast.Title>
          <Toast.Message>{t("components.DetailsTab.failedToFetchDesc")}</Toast.Message>
        </Toast>,
        { position: "top-right" },
      );
    },
  });

  const hasChanges = useMemo(() => {
    return data?.description && descriptionValue && !isEqual(data.description, descriptionValue);
  }, [data?.description, descriptionValue]);

  if (loading) {
    return <Loading />;
  }

  if (!hasConversation) {
    return (
      <Styled.EmptyStateWrapper>
        <EmptyState />
        <Text weight={5} size={5} lineHeight={4}>
          {t("components.DetailSteps.noSummaryToPreview")}
        </Text>
      </Styled.EmptyStateWrapper>
    );
  }

  const regenButton = (
    <Button
      disabled={loading}
      color="dark"
      variant="outlined"
      onClick={() =>
        updateDescriptionMutation.mutate({
          generate: true,
        })
      }
    >
      {t("components.DetailsTab.regenerate")}
    </Button>
  );

  let header;
  let body;
  if (editing) {
    header = (
      <>
        {regenButton}
        <Button
          disabled={loading}
          color="dark"
          variant="ghost"
          onClick={() => {
            setDescriptionValue(cloneDeep(data?.description));
            setEditing(false);
          }}
        >
          {t("components.DetailsTab.cancel")}
        </Button>
        <Button
          disabled={loading || !hasChanges}
          color="dark"
          variant="outlined"
          onClick={() => {
            updateDescriptionMutation.mutate({
              description: descriptionValue || [],
              generate: false,
            });
            setEditing(false);
          }}
        >
          {t("components.DetailsTab.save")}
        </Button>
      </>
    );
    body = (
      <DetailsCard
        editable={true}
        taskDescription={descriptionValue || []}
        onChange={setDescriptionValue}
      />
    );
  } else {
    header = (
      <>
        {regenButton}
        <Button disabled={loading} color="dark" variant="outlined" onClick={() => setEditing(true)}>
          {t("components.DetailsTab.edit")}
        </Button>
      </>
    );
    body = (
      <DetailSteps
        workflowId={workflowId}
        taskId={taskId}
        exampleSetId={exampleSetId}
        isOpen={true}
      />
    );
  }

  return (
    <Styled.DetailsTab>
      <Styled.DetailsTabHeader>{header}</Styled.DetailsTabHeader>
      <Styled.DetailsTabBody>{body}</Styled.DetailsTabBody>
    </Styled.DetailsTab>
  );
};
