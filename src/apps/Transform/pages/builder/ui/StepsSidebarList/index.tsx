import { Fragment, useEffect, useState } from "react";
import { range } from "es-toolkit";
import { useMatch, useNavigate, useParams } from "react-router-dom";
import { useMutationState } from "@tanstack/react-query";
import { Tooltip } from "@floqastinc/flow-ui_core";
import { NewTaskPopover } from "../NewTaskPopover";
import { Step } from "./ui/Step";
import { StepButton } from "./ui/StepButton";
import * as Styled from "./index.styles";
import { useTasks } from "@v3/tasks";
import { PackageInput } from "@/svg/PackageInput";
import { useFeatureFlags } from "@/components/FeatureFlag";
import { AGENTS, BUILDER, INPUTS, INPUTS_PATH, V3 } from "@/constants";
import { AddStep } from "@/svg/AddStep";
import { t } from "@/utils/i18n";

export const StepsSidebarList = () => {
  const { getFlag } = useFeatureFlags();
  const onInputsPage = useMatch(`/${BUILDER}/${V3}/${INPUTS_PATH}`);
  const { workflowId = "", taskId = "" } = useParams();
  const navigate = useNavigate();

  const isGlobalInputsEnabled = getFlag("enable-global-inputs");
  const isAdvancedStepManagementEnabled = getFlag("transform-advanced-step-manager");

  const tasksQuery = useTasks(
    {
      workflowId,
    },
    {
      enabled: !!workflowId,
    },
  );

  const deletionState = useDeletionState({ workflowId });

  const createTaskState = useMutationState({
    filters: {
      status: "pending",
      mutationKey: ["createTask", workflowId],
    },
  });
  const [createTaskWasPending, setCreateTaskWasPending] = useState(false);
  const createTaskIsPending = createTaskState.some((state) => state.status === "pending");
  const tasksExist = !!tasksQuery.data?.length;

  useEffect(() => {
    // createTaskWasPending is some adjusted state to keep a square up during
    //  the gap between when the createTask is finished and the invalidated getTasks
    //  gets refetched
    if (!tasksQuery.isFetching) {
      setCreateTaskWasPending(createTaskIsPending);
    }

    if (createTaskWasPending && !tasksQuery.isFetching && !createTaskIsPending) {
      setCreateTaskWasPending(false);
    }
  }, [createTaskIsPending, tasksQuery.isFetching, createTaskWasPending]);

  return (
    <>
      {isGlobalInputsEnabled && (
        <>
          <Tooltip>
            <Tooltip.Trigger>
              <StepButton
                // @todo: use route
                isActive={!!onInputsPage}
                onClick={() => navigate(`/${BUILDER}/${V3}/${AGENTS}/${workflowId}/${INPUTS}`)}
              >
                <PackageInput />
              </StepButton>
            </Tooltip.Trigger>
            <Styled.TooltipContent hasArrow side="right">
              {t("components.StepsSidebarList.inputs")}
            </Styled.TooltipContent>
          </Tooltip>
          <Styled.StepSeparator>
            {isAdvancedStepManagementEnabled && !createTaskIsPending && tasksExist ? (
              <NewTaskPopover
                trigger={
                  <Styled.AddStepSeparatorButton>
                    <AddStep />
                  </Styled.AddStepSeparatorButton>
                }
                labels={{
                  title: t("components.StepsSidebarList.addStep"),
                  strategy: t("components.StepsSidebarList.blockType"),
                  submitButton: t("components.StepsSidebarList.add"),
                }}
              />
            ) : (
              <></>
            )}
          </Styled.StepSeparator>
        </>
      )}

      {tasksQuery.isPending && !tasksQuery.data
        ? range(0, 30).map((i) => (
            <>
              <StepButton.Pending key={`step-${i}`} />
              <Styled.StepSeparator />
            </>
          ))
        : null}
      {tasksQuery.data
        ? tasksQuery.data.map((task, i) => {
            if (
              deletionState.taskIndexDeleted &&
              deletionState.taskIndexDeleted !== -1 &&
              i >= deletionState.taskIndexDeleted
            ) {
              return (
                <>
                  <StepButton.Pending />
                  <Styled.StepSeparator />
                </>
              );
            }

            const isNotLastTask = i !== tasksQuery.data.length - 1;
            const taskActive = tasksQuery.data[i]?.status === "ACTIVE";
            const showAddStepCTA = isNotLastTask && taskActive && !createTaskIsPending;

            return (
              <Fragment key={`${task.id}-step-container`}>
                <Tooltip>
                  <Tooltip.Trigger>
                    <StepButton
                      isActive={taskId === task.id}
                      onClick={() => {
                        navigate(`/builder/v3/agents/${workflowId}/steps/${task.id}`);
                      }}
                    >
                      <Step taskId={task.id} stepNum={i + 1} />
                    </StepButton>
                  </Tooltip.Trigger>
                  <Styled.TooltipContent hasArrow side="right">
                    {task.name}
                  </Styled.TooltipContent>
                </Tooltip>
                <Styled.StepSeparator>
                  {isAdvancedStepManagementEnabled && showAddStepCTA ? (
                    <NewTaskPopover
                      trigger={
                        <Styled.AddStepSeparatorButton>
                          <AddStep />
                        </Styled.AddStepSeparatorButton>
                      }
                      labels={{
                        title: t("components.StepsSidebarList.addStep"),
                        strategy: t("components.StepsSidebarList.blockType"),
                        submitButton: t("components.StepsSidebarList.add"),
                      }}
                    />
                  ) : (
                    <></>
                  )}
                </Styled.StepSeparator>
              </Fragment>
            );
          })
        : null}
      {createTaskWasPending ? (
        <>
          <StepButton.Pending />
          <Styled.StepSeparator />
        </>
      ) : null}
    </>
  );
};

// TODO: This still flashes the deleted step for some reason when
//  the invalidated query refetches.
// This might be a more complex problem dealing with this entire component
//  getting remounted and thus wiping out state.
const useDeletionState = ({ workflowId }: { workflowId: string }) => {
  const navigate = useNavigate();

  const tasksQuery = useTasks(
    {
      workflowId,
    },
    {
      enabled: !!workflowId,
    },
  );

  // NOTE: These two mutations are compound operations and not
  // defined within the API SDK. This is why they have bespoke mutationKeys
  // and don't have a utility associated with getting their state yet yet yet yet.
  const deleteTaskState = useMutationState({
    filters: {
      status: "pending",
      mutationKey: ["deleteTask", { workflowId }],
    },
  });
  const [deleteTaskWasPending, setDeleteTaskWasPending] = useState(false);
  const [taskIndexToBeDeleted, setTaskIndexToBeDeleted] = useState<number | null>(null);
  const deleteTaskIsPending = deleteTaskState.some((state) => state.status === "pending");
  // This relies on the deleteTask logic which deletes many tasks,
  // but takes as an argument the first task to be deleted (e.g. the task
  // after which all subsequent tasks will be deleted)
  const earliestTaskIdToBeDeleted = deleteTaskState.find((state) => {
    // @ts-expect-error type narrowing woes
    return state.variables && typeof state.variables === "object"
      ? state.variables["taskId"]
      : undefined;
    // @ts-expect-error more type narrowing stuff
  })?.variables?.taskId;
  const earliestTaskIdToBeDeletedIndex = tasksQuery.data?.findIndex(
    (task) => task.id === earliestTaskIdToBeDeleted,
  );
  useEffect(() => {
    if (earliestTaskIdToBeDeletedIndex && earliestTaskIdToBeDeletedIndex !== -1) {
      setTaskIndexToBeDeleted(earliestTaskIdToBeDeletedIndex);
    }
  }, [earliestTaskIdToBeDeletedIndex]);

  useEffect(() => {
    if (!deleteTaskWasPending && deleteTaskIsPending) {
      setDeleteTaskWasPending(deleteTaskIsPending);
    }

    const isSameTask =
      tasksQuery.data?.length && taskIndexToBeDeleted !== null
        ? tasksQuery.data[taskIndexToBeDeleted]?.id === earliestTaskIdToBeDeleted
        : false;

    // Loading has completed, flush stored state
    if (deleteTaskWasPending && !isSameTask && !deleteTaskIsPending) {
      setDeleteTaskWasPending(false);
      setTaskIndexToBeDeleted(null);
      // For some reason, navigation doesn't work here...
      //  This is the navigation that would have normally been moved from
      //   ConfirmStepDeletionDialog. This is for preventing a flash of
      //   old data, since it seems like the component gets unmounted completely.
      // const newLastTask = tasksQuery.data?.at(-1)
      // console.log('newLastTask', newLastTask, taskIndexToBeDeleted);
      // if (newLastTask) {
      //   console.log('why...')
      //   redirect(`/builder`);
      // }
    }
  }, [
    deleteTaskIsPending,
    tasksQuery.data,
    deleteTaskWasPending,
    navigate,
    taskIndexToBeDeleted,
    workflowId,
  ]);

  return {
    isPending: deleteTaskWasPending,
    taskIndexDeleted: taskIndexToBeDeleted,
  };
};
