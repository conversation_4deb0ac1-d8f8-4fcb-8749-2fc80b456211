import { useState } from "react";
import { Flex, Text, Fixed, IconButton } from "@floqastinc/flow-ui_core";
import { PictureAsPdf, ArrowLeftAlt, ArrowRightAlt } from "@floqastinc/flow-ui_icons";
import { Document, Page, pdfjs } from "react-pdf";

// style sheet for framework... may need annotations and more later.
import "react-pdf/dist/Page/TextLayer.css";
import { t } from "@/utils/i18n";
import { Loading } from "@/components/Loading";

// Configure parsing worker settings
pdfjs.GlobalWorkerOptions.workerSrc = new URL(
  "pdfjs-dist/build/pdf.worker.min.mjs",
  import.meta.url,
).toString();
const options = {
  cMapUrl: `https://unpkg.com/pdfjs-dist@${pdfjs.version}/cmaps/`,
};

type PDFViewerProps = {
  file: File;
};
export const PDFViewer = ({ file }: PDFViewerProps) => {
  const [pageNumber, setPageNumber] = useState<number>(-1);
  const [numPages, setNumPages] = useState<number>(0);

  const onDocumentLoadSuccess = ({ numPages }: { numPages: number }): void => {
    setNumPages(numPages);
    setPageNumber(1);
  };
  return (
    <Flex
      direction={"column"}
      style={{
        width: "fit-content",
        maxWidth: "100%",
        backgroundColor: "var(--flo-sem-color-surface-neutral-weakest)",
      }}
    >
      <Flex
        gap={10}
        align="center"
        style={{
          padding: "10px",
          height: "3rem",
          border: "0.68px solid var(--flo-sem-color-surface-neutral-weak)",
          backgroundColor: "white",
        }}
      >
        <PictureAsPdf color="var(--flo-sem-color-content-neutral-medium)" />
        <Text weight={5} color="var(--flo-sem-color-content-neutral-strongest)">
          {file.name}
        </Text>
        <Fixed xEnd>
          {numPages ? (
            <Flex align="center">
              <IconButton name="arrow-left">
                <ArrowLeftAlt color="var(--flo-sem-color-content-neutral-medium)" />
              </IconButton>
              <Text weight={5} color="var(--flo-sem-color-content-neutral-strongest)">
                {t("components.PDFViewer.pageXofY", { x: pageNumber, y: numPages })}
              </Text>
              <IconButton name="arrow-right">
                <ArrowRightAlt color="var(--flo-sem-color-content-neutral-medium)" />
              </IconButton>
            </Flex>
          ) : null}
        </Fixed>
      </Flex>
      <div
        style={{
          overflow: "auto",
          margin: 18,
          border: "0.68px solid var(--flo-sem-color-surface-neutral-weak)",
        }}
      >
        <Document
          options={options}
          file={file}
          onLoadSuccess={onDocumentLoadSuccess}
          loading={<Loading />}
        >
          <Page pageNumber={pageNumber} />
        </Document>
      </div>
    </Flex>
  );
};
